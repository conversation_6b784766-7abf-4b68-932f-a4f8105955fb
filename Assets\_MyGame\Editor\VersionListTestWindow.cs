using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace WGame.Editor
{
    /// <summary>
    /// 版本号列表测试窗口
    /// </summary>
    public class VersionListTestWindow : EditorWindow
    {
        private bool showVersionList = false;
        private List<string> versions = new List<string> { "1.0.0", "1.0.1", "1.1.0" };
        private string newVersion = "";

        [MenuItem("Build/Test Version List Window", priority = 200)]
        public static void ShowWindow()
        {
            var window = GetWindow<VersionListTestWindow>("版本号列表测试");
            window.Show();
        }

        private void OnGUI()
        {
            GUILayout.Label("版本号列表测试窗口", EditorStyles.boldLabel);
            GUILayout.Space(10);

            // 测试Foldout
            showVersionList = EditorGUILayout.Foldout(showVersionList, $"📋 版本号列表 ({versions.Count} 个)");

            if (showVersionList)
            {
                EditorGUI.indentLevel++;
                
                GUILayout.Space(5);
                
                // 添加版本号
                GUILayout.BeginHorizontal();
                GUILayout.Label("新版本号:", GUILayout.Width(80));
                newVersion = EditorGUILayout.TextField(newVersion);
                if (GUILayout.Button("添加", GUILayout.Width(50)))
                {
                    if (!string.IsNullOrEmpty(newVersion) && !versions.Contains(newVersion))
                    {
                        versions.Add(newVersion);
                        newVersion = "";
                    }
                }
                GUILayout.EndHorizontal();
                
                GUILayout.Space(5);
                
                // 显示版本号列表
                for (int i = 0; i < versions.Count; i++)
                {
                    GUILayout.BeginHorizontal();
                    GUILayout.Label($"• {versions[i]}");
                    if (GUILayout.Button("删除", GUILayout.Width(50)))
                    {
                        versions.RemoveAt(i);
                        break;
                    }
                    GUILayout.EndHorizontal();
                }
                
                if (versions.Count == 0)
                {
                    GUILayout.Label("暂无版本号", EditorStyles.centeredGreyMiniLabel);
                }
                
                EditorGUI.indentLevel--;
            }
            
            GUILayout.Space(10);
            GUILayout.Label($"当前Foldout状态: {showVersionList}");
        }
    }
}
