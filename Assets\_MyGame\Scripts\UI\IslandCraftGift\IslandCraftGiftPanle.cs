using System;
using System.Collections.Generic;
using System.Linq;
using Common.NetMsg;
using FairyGUI;
using Google.Protobuf.Collections;
using Proto.LogicData;

public class IslandCraftGiftPanle : Panel
{
    private GTextField lblLeftTime;
    private GList listSkinActGift;
    private List<BaseNetActivityInfo> activityInfos = new List<BaseNetActivityInfo>();

    public IslandCraftGiftPanle()
    {
        packName = "IslandCraftGift";
        compName = "IslandCraftGiftPanle";
        modal = true;
    }

    protected override void DoInitialize()
    {
        lblLeftTime = contentPane.GetChild("lblLeftTime").asTextField;
        listSkinActGift = contentPane.GetChild("listSkinActGift").asList;
        listSkinActGift.itemRenderer = UpdateGiftListItem;

        Request();
    }

    private void Request()
    {
        NetMgr.RequestActivitiesInfo((int)ActivityDisplayUiEnum.IslandCraftGift);
    }

    protected override void OnMessageRecieve(NetMessage msg)
    {
        var cmd = msg.GetCmd();
        var data = msg.data;
        switch (cmd)
        {
            case NetMsg.S2CRequestActivitiesInfoResult:
                var tempdataInfo = msg.Parse<RequestActivitiesInfoResult>();
                if (tempdataInfo.DisplayUi == (int)ActivityDisplayUiEnum.IslandCraftGift)
                {
                    if (tempdataInfo.ActivityList.Count > 0)
                    {
                        activityInfos.Clear();
                        activityInfos = tempdataInfo.ActivityList.ToList();
                        UpdateActivityInfo();
                    }
                    else
                    {
                        Hide();
                    }

                }
                break;
            case NetMsg.S2CGetActivityRewardResult:
                var rewardResult = msg.Parse<RequestGetActivityRewardResult>();
                if (LangUtil.CheckResult(rewardResult.Result) && rewardResult.DisplayUi == (int)ActivityDisplayUiEnum.IslandCraftGift)
                {
                    TipMgr.ShowReward(rewardResult.ItemList, () =>
                    {
                        Request();
                    });
                }
                break;
        }
    }

    private void UpdateActivityInfo()
    {
        if (activityInfos == null || activityInfos.Count == 0)
        {
            return;
        }
        UpdateLeftTime(activityInfos[activityInfos.Count - 1].CdTime);
        activityInfos.Sort((x, y) =>
        {
            bool xCompleted = x.ReqCount <= x.CurCount;
            bool yCompleted = y.ReqCount <= y.CurCount;
            return xCompleted.CompareTo(yCompleted);
        });

        listSkinActGift.data = activityInfos;
        listSkinActGift.numItems = activityInfos.Count;
    }

    private void UpdateLeftTime(int leftTime)
    {
        if (leftTime > 0)
        {
            UILeftTimer.Create(lblLeftTime, leftTime, (time) =>
            {
                lblLeftTime.text = DateUtil.ParseDayHoureMinutes(time);
                if (time <= 0)
                {
                    Hide();
                }
            });
        }
    }

    private void UpdateGiftListItem(int index, GObject obj)
    {
        var data = (obj.parent.data as List<BaseNetActivityInfo>)[index];
        var listReward = obj.asCom.GetChild("listReward").asList;
        listReward.itemRenderer = OnUpdateRewardItem;
        listReward.data = data.RewardItemList;
        listReward.numItems = data.RewardItemList.Count;
        var lblGoodsName = obj.asCom.GetChild("lblGoodsName").asTextField;
        var lblCount = obj.asCom.GetChild("lblCount").asTextField;
        var lblPrice = obj.asCom.GetChild("lblPrice").asTextField;
        var lblLimitCount = obj.asCom.GetChild("lblLimitCount").asTextField;
        var lblLimitType = obj.asCom.GetChild("lblLimitType").asTextField;

        lblGoodsName.text = data.Desc;
        var ctrl = obj.asCom.GetController("c1");
        lblLimitType.text = LangUtil.GetText("txtGiftrefreshType" + data.ReqParam3);
        lblLimitCount.text = data.CurCount + "/" + data.ReqCount;

        obj.onClick.Clear();
        // 0 广告 1 钻石 2 rmb 3 已领取

        if (data.ReqCount <= data.CurCount)
        {
            ctrl.selectedIndex = 3;
        }
        else if (data.Param0 == 1)
        {
            ctrl.selectedIndex = 1;
            lblCount.text = data.ReqParam1 + "";
            obj.onClick.Add(() =>
            {
                TipMgr.CheckResourceNotEnough(ItemId.Daimond, data.ReqParam1, Session.playerLobbyInfo.Gold,
                       enoughCallback: () =>
                       {
                           TipMgr.ShowMsgBox(LangUtil.GetText("txtBuyItemTips", data.Desc), () =>
                           {
                               NetMgr.GetActivityReward(data.Id);
                           }, () => { });
                       },
                       jumpCallback: () =>
                       {
                           Hide();
                       });
            });
        }
        else if (data.Param0 == 2 && data.ChargeInfoList.Count > 0)
        {
            ctrl.selectedIndex = 2;
            //rmb购买
            lblPrice.text = LangUtil.GetText("txtRmb", data.ChargeInfoList[0].Rmb);
            obj.onClick.Add(() =>
            {
                Platform.GetInstance().Recharge(data.ChargeInfoList[0], data.ReqParam1 + "");
            });
        }
        else if (data.Param0 == 3)
        {
            ctrl.selectedIndex = 0;
            obj.onClick.Add(() =>
            {
                // NetMgr.GetPlayerWatchVideo(data.Id, (int)PlayerWatchVideoEnum.WatchVideoIslandCraftGift, WatchVideoAdId.WatchVideoIslandCraftGift);
            });
        }
        else
        {
            ctrl.selectedIndex = 0;
            obj.onClick.Add(() =>
            {
                // NetMgr.GetPlayerWatchVideo(data.Id, (int)PlayerWatchVideoEnum.WatchVideoIslandCraftGift, WatchVideoAdId.WatchVideoIslandCraftGift);
            });
        }
    }

    private void OnUpdateRewardItem(int index, GObject obj)
    {
        var data = (obj.parent.data as RepeatedField<BaseNetItemInfo>)[index];
        var bg = obj.asCom.GetChild("bg").asLoader;
        var icon = obj.asCom.GetChild("icon").asLoader;
        var lblItemCount = obj.asCom.GetChild("lblItemCount").asTextField;
        var itemInfo = ConfigItem.GetData((int)data.ItemId);

        bg.url = itemInfo.qualityUrl;
        icon.url = itemInfo.iconUrl;
        lblItemCount.text = MathUtil.ConvertNumWithUnit(data.Count);
        TipMgr.SetItemTip(icon, itemInfo.id);
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                this.Hide();
                break;
        }
    }
}