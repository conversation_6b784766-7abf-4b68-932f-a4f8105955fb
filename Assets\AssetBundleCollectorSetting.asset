%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 185f6993d5150494d98da50e26cb1c25, type: 3}
  m_Name: AssetBundleCollectorSetting
  m_EditorClassIdentifier: 
  ShowPackageView: 0
  EnableAddressable: 0
  LocationToLower: 0
  IncludeAssetGUID: 0
  UniqueBundleName: 0
  ShowEditorAlias: 1
  Packages:
  - PackageName: DefaultPackage
    PackageDesc: 
    Groups:
    - GroupName: Default Group
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/_MyGame/Bundles/Audio/Bgm
        CollectorGUID: e53dd408bdcdcf049b8bfc9c1a1b9ad3
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Audio/Sound
        CollectorGUID: 6703f88fb37e69a4495e9c4fbc838c4c
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/DropItem
        CollectorGUID: ace30edec7f1d004196f963b6cd94ec2
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Effect
        CollectorGUID: 99a91d4dce1ee7943802059635f911f7
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/EnemyNormal
        CollectorGUID: db7c98f63c96fa44abba232989f42076
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Res/Enemy/Enemy
        CollectorGUID: 9cd7803711db1054da22b7324ee17a29
        CollectorType: 1
        AddressRuleName: AddressByFileName
        PackRuleName: PackTopDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/EnemyBossElite
        CollectorGUID: 824ca2d7cc2c9fc4d8e9fa0c414f4c24
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Weapon/emitor
        CollectorGUID: d8e141be681374f40877a61d33ed0e48
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Weapon/enemyBullet
        CollectorGUID: 86b659a081099f144810588148881025
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Weapon/playerBullet
        CollectorGUID: e2a2dccb1dbed9c4f8cf1f2e00b48f67
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Weapon/artifactBullet
        CollectorGUID: e2dc2b24d14db824e84da676c5e839df
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Weapon/petBullet
        CollectorGUID: f8e23524337eb8c458ca3a02eb2cd91c
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Weapon/wallBullet
        CollectorGUID: a107c49e01a95b648851128ea5b31f3c
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Fonts
        CollectorGUID: ca7e3935b94636a4ba9784daabdce9df
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Heroes
        CollectorGUID: dd93d486c352d5f4384ed29f708bf23c
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Map
        CollectorGUID: 2380b20d75729cf4ca9eb5029c623dcb
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Scenes
        CollectorGUID: 1bdbf994aa0c4804ba63e9a53be12e5b
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Shader
        CollectorGUID: e105a130de6401f4486819f1df7e1021
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/UI
        CollectorGUID: 79af74fef4da41341af13c74e3b4dc66
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/DataSO
        CollectorGUID: 41f43c136e3de2740ba8b624472721b3
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Runner
        CollectorGUID: 8b43e062781ed4e469a8bea575bc0a10
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Decoration
        CollectorGUID: 8b927e3573b69d94ea114f867e7109fb
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Pets
        CollectorGUID: e94e7fd137462af4f8f4ffdd6942c29a
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Artifact
        CollectorGUID: a3a74106637b6d94da95c0bab65c220c
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Wall
        CollectorGUID: e62468f0ee4251249a83a38fb5f2c4be
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/MidWay
        CollectorGUID: f83cce5150da75d458fa52ea8286dbe9
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/TreasureRaiders
        CollectorGUID: 1a6167105ca20b246a30a8a536c11c86
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/FlyBullet
        CollectorGUID: 92d17b9197162f54082b2d008d52b3af
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/MiniGame/Effect
        CollectorGUID: a89a95ad0733d1845a969968be011761
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/MiniGame/buff
        CollectorGUID: 5bb0ab636b8012f4abf7074e66000358
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Dragon
        CollectorGUID: af5c594ec340c2e498266ba827068a70
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/HeroEleEffect
        CollectorGUID: 9a938d6c51c394e418cf24cfe72d0b75
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/DragonGear
        CollectorGUID: 6b812f957f0138447a7a74b447a6063b
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/MiniGame/GearDragon/gear
        CollectorGUID: 959444cc76cfbc14ca62452ab47469ec
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/MiniGame/GearDragon/hero
        CollectorGUID: e3bcd69c974a1e244b63b4d1a0582404
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/AOT
        CollectorGUID: 18e5d1d9516ef464da74e9e32c04409e
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackTopDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Game
        CollectorGUID: 8b30ef2684d8e384fa784e8ca46f1853
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackTopDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
