using System;
using System.Collections.Generic;
using UnityEngine;

namespace WGame.Editor
{
    /// <summary>
    /// 构建版本号配置
    /// </summary>
    [CreateAssetMenu(fileName = "BuildVersionConfig", menuName = "WGame/Build Version Config")]
    public class BuildVersionConfig : ScriptableObject
    {
        [Header("版本号列表配置")]
        [SerializeField]
        private List<VersionItem> versionItems = new List<VersionItem>();

        /// <summary>
        /// 获取所有版本号项目
        /// </summary>
        public List<VersionItem> VersionItems => versionItems;

        /// <summary>
        /// 获取选中的版本号列表
        /// </summary>
        public string[] GetSelectedVersions()
        {
            var selectedVersions = new List<string>();
            foreach (var item in versionItems)
            {
                if (item.isSelected && !string.IsNullOrEmpty(item.version))
                {
                    selectedVersions.Add(item.version);
                }
            }
            return selectedVersions.ToArray();
        }

        /// <summary>
        /// 添加版本号
        /// </summary>
        public void AddVersion(string version)
        {
            if (string.IsNullOrEmpty(version))
                return;

            // 检查是否已存在
            foreach (var item in versionItems)
            {
                if (item.version == version)
                {
                    Debug.LogWarning($"版本号 {version} 已存在");
                    return;
                }
            }

            versionItems.Add(new VersionItem { version = version, isSelected = true });
            MarkDirty();
        }

        /// <summary>
        /// 移除版本号
        /// </summary>
        public void RemoveVersion(string version)
        {
            for (int i = versionItems.Count - 1; i >= 0; i--)
            {
                if (versionItems[i].version == version)
                {
                    versionItems.RemoveAt(i);
                    MarkDirty();
                    break;
                }
            }
        }

        /// <summary>
        /// 移除指定索引的版本号
        /// </summary>
        public void RemoveVersionAt(int index)
        {
            if (index >= 0 && index < versionItems.Count)
            {
                versionItems.RemoveAt(index);
                MarkDirty();
            }
        }

        /// <summary>
        /// 设置版本号选中状态
        /// </summary>
        public void SetVersionSelected(int index, bool selected)
        {
            if (index >= 0 && index < versionItems.Count)
            {
                versionItems[index].isSelected = selected;
                MarkDirty();
            }
        }

        /// <summary>
        /// 设置版本号选中状态
        /// </summary>
        public void SetVersionSelected(string version, bool selected)
        {
            foreach (var item in versionItems)
            {
                if (item.version == version)
                {
                    item.isSelected = selected;
                    MarkDirty();
                    break;
                }
            }
        }

        /// <summary>
        /// 清空所有版本号
        /// </summary>
        public void ClearVersions()
        {
            versionItems.Clear();
            MarkDirty();
        }

        /// <summary>
        /// 标记为已修改
        /// </summary>
        private void MarkDirty()
        {
#if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
#endif
        }

        /// <summary>
        /// 获取选中版本数量
        /// </summary>
        public int GetSelectedCount()
        {
            int count = 0;
            foreach (var item in versionItems)
            {
                if (item.isSelected)
                    count++;
            }
            return count;
        }

        /// <summary>
        /// 全选/取消全选
        /// </summary>
        public void SetAllSelected(bool selected)
        {
            foreach (var item in versionItems)
            {
                item.isSelected = selected;
            }
            MarkDirty();
        }
    }

    /// <summary>
    /// 版本号项目
    /// </summary>
    [Serializable]
    public class VersionItem
    {
        [SerializeField]
        public string version;
        
        [SerializeField]
        public bool isSelected;

        public VersionItem()
        {
            version = "";
            isSelected = true;
        }

        public VersionItem(string version, bool isSelected = true)
        {
            this.version = version;
            this.isSelected = isSelected;
        }
    }
}
