using UnityEditor;
using UnityEngine;
using System.IO;
using System.Collections.Generic;
using System;
using System.IO.Compression;
using UnityEditor.Build.Reporting;

namespace WGame.Editor
{
    /// <summary>
    /// 统一构建窗口
    /// 支持Android和iOS平台的构建操作
    /// </summary>
    public class BuildWindow : EditorWindow
    {
        private string version = "";
        private int versionCode = 1;
        private BuildTarget selectedPlatform = BuildTarget.Android;
        private int platformTabIndex = 0; // 0=Android, 1=iOS
        private string selectedPlatformId = "google"; // Android平台ID选择

        // 版本号列表管理相关字段
        private Vector2 versionListScrollPos;
        private string newVersionInput = "";
        private bool showVersionListSection = false;
        private static List<VersionItem> versionItems = new List<VersionItem>();

        // 简化的版本号项目类
        [System.Serializable]
        public class VersionItem
        {
            public string version;
            public bool isSelected;

            public VersionItem(string version, bool isSelected = true)
            {
                this.version = version;
                this.isSelected = isSelected;
            }
        }

        [MenuItem("Build/BuildWindow", priority = 10)]
        public static void ShowBuildWindow()
        {
            var window = GetWindow<BuildWindow>(false, "🔧 发布", true);
            window.LoadConfigData();
            window.titleContent = new GUIContent("🔧 发布");
            window.minSize = new Vector2(600, 500);
            window.maxSize = new Vector2(600, 500);
            window.Show();
        }

        /// <summary>
        /// 加载配置数据
        /// </summary>
        private void LoadConfigData()
        {
            // 从GameConfig读取版本信息（GameConfig现在从Resources读取）
            version = GameConfig.GetVer();
            versionCode = GameConfig.GetBuildVer();

            // 从VersionManager读取platformId
            var versionData = VersionManager.GetVersionData();
            selectedPlatformId = versionData?.platformId ?? "google";

            // 设置默认平台
#if UNITY_ANDROID
            selectedPlatform = BuildTarget.Android;
            platformTabIndex = 0;
#elif UNITY_IOS
            selectedPlatform = BuildTarget.iOS;
            platformTabIndex = 1;
#else
            selectedPlatform = BuildTarget.Android;
            platformTabIndex = 0;
#endif

            Debug.Log($"🔄 [BuildWindow] 加载配置 - 版本: {version}, 代码: {versionCode}, 平台: {selectedPlatform}, 平台ID: {selectedPlatformId}");

            // 初始化版本号列表（如果为空）
            if (versionItems.Count == 0)
            {
                versionItems.Add(new VersionItem("1.0.0", true));
                versionItems.Add(new VersionItem("1.0.1", true));
                versionItems.Add(new VersionItem("1.1.0", false));
            }
        }

        /// <summary>
        /// 保存配置数据到Resources中的version.json
        /// </summary>
        private void SaveConfigData()
        {
            // 对于Android平台，传递选择的platformId；对于其他平台，传递null使用默认值
            string platformIdToSave = selectedPlatform == BuildTarget.Android ? selectedPlatformId : null;
            VersionEditor.UpdateVersion(version, versionCode, platformIdToSave);
        }

        private void OnGUI()
        {
            // 设置背景色
            var originalColor = GUI.backgroundColor;
            var buttonColor = new Color(0.3f, 0.6f, 0.9f, 1f);
            var stepButtonColor = new Color(0.2f, 0.7f, 0.3f, 1f);
            var sidebarColor = new Color(0.3f, 0.3f, 0.3f, 1f);

            // 头部区域
            GUILayout.Space(10);

            // 主要内容区域 - 水平布局
            GUILayout.BeginHorizontal();

            // 左侧平台选择区域
            GUI.backgroundColor = sidebarColor;
            GUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.Width(120));

            var platformLabelStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 12,
                alignment = TextAnchor.MiddleCenter,
                normal = { textColor = Color.white }
            };

            GUILayout.Space(5);
            GUILayout.Label("平台选择", platformLabelStyle);
            GUILayout.Space(10);

            // 平台按钮样式
            var platformButtonStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 11,
                fontStyle = FontStyle.Bold,
                fixedHeight = 35,
                alignment = TextAnchor.MiddleCenter
            };

            // Android平台按钮
            GUI.backgroundColor = platformTabIndex == 0 ? buttonColor : Color.gray;
            platformButtonStyle.normal.textColor = platformTabIndex == 0 ? Color.white : Color.gray;
            if (GUILayout.Button("📱 Android", platformButtonStyle))
            {
                platformTabIndex = 0;
                selectedPlatform = BuildTarget.Android;
            }

            GUILayout.Space(5);

            // iOS平台按钮
            GUI.backgroundColor = platformTabIndex == 1 ? buttonColor : Color.gray;
            platformButtonStyle.normal.textColor = platformTabIndex == 1 ? Color.white : Color.gray;
            if (GUILayout.Button("🍎 iOS", platformButtonStyle))
            {
                platformTabIndex = 1;
                selectedPlatform = BuildTarget.iOS;
            }

            GUILayout.FlexibleSpace();
            GUILayout.EndVertical();
            GUI.backgroundColor = originalColor;

            GUILayout.Space(10);

            // 右侧设置区域
            GUILayout.BeginVertical();

            // 版本设置区域
            var inputAreaStyle = new GUIStyle(EditorStyles.helpBox)
            {
                padding = new RectOffset(15, 15, 10, 10)
            };

            GUILayout.BeginVertical(inputAreaStyle);

            var labelStyle = new GUIStyle(EditorStyles.label)
            {
                fontStyle = FontStyle.Bold,
                normal = { textColor = new Color(0.8f, 0.8f, 0.8f, 1f) }
            };

            var textFieldStyle = new GUIStyle(EditorStyles.textField)
            {
                fontSize = 12,
                fixedHeight = 22
            };

            // 版本号输入
            GUILayout.BeginHorizontal();
            GUILayout.Label("📱 版本号 (Version)", labelStyle, GUILayout.Width(140));
            version = EditorGUILayout.TextField(version, textFieldStyle);
            GUILayout.EndHorizontal();

            GUILayout.Space(8);

            // 版本代码输入
            GUILayout.BeginHorizontal();
            GUILayout.Label("🔢 版本代码 (VersionCode)", labelStyle, GUILayout.Width(140));
            versionCode = EditorGUILayout.IntField(versionCode, textFieldStyle);
            GUILayout.EndHorizontal();

            GUILayout.Space(8);

            // 热更版本号显示（只读）
            GUILayout.BeginHorizontal();
            GUILayout.Label("🔥 热更版本号 (ResVersion)", labelStyle, GUILayout.Width(140));

            var readOnlyStyle = new GUIStyle(EditorStyles.textField)
            {
                fontSize = 12,
                fixedHeight = 22
            };
            readOnlyStyle.normal.textColor = Color.gray;

            EditorGUILayout.TextField(ResVersion.version.ToString(), readOnlyStyle);
            GUILayout.EndHorizontal();

            // Android平台ID选择（仅Android平台显示）
            if (selectedPlatform == BuildTarget.Android)
            {
                GUILayout.Space(8);
                GUILayout.BeginHorizontal();
                GUILayout.Label("🏷️ 平台ID (PlatformId)", labelStyle, GUILayout.Width(140));

                // 创建平台ID选项
                string[] platformIdOptions = { "google", "official" };
                string[] platformIdDisplayNames = { "Google Play", "官方版本" };

                int currentIndex = System.Array.IndexOf(platformIdOptions, selectedPlatformId);
                if (currentIndex == -1) currentIndex = 0; // 默认选择第一个

                int newIndex = EditorGUILayout.Popup(currentIndex, platformIdDisplayNames, textFieldStyle);
                selectedPlatformId = platformIdOptions[newIndex];

                GUILayout.EndHorizontal();
            }

            GUILayout.EndVertical();

            GUILayout.Space(15);

            // 版本号列表管理区域
            DrawVersionListSection(inputAreaStyle, labelStyle, textFieldStyle);

            GUILayout.Space(15);

            // 构建操作按钮区域
            var stepAreaStyle = new GUIStyle(EditorStyles.helpBox)
            {
                padding = new RectOffset(15, 15, 15, 15)
            };

            GUILayout.BeginVertical(stepAreaStyle);

            var stepTitleStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 14,
                alignment = TextAnchor.MiddleCenter,
                normal = { textColor = new Color(0.9f, 0.9f, 0.9f, 1f) }
            };

            GUILayout.Label("🔧 构建操作", stepTitleStyle);
            GUILayout.Space(10);

            var stepButtonStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 12,
                fontStyle = FontStyle.Bold,
                fixedHeight = 40,
                normal = { textColor = Color.white }
            };

            // 生成热更dll和生成强更包在同一行
            GUILayout.BeginHorizontal();

            // 生成热更dll（左边）
            GUI.backgroundColor = stepButtonColor;
            if (GUILayout.Button("🔥 生成热更DLL", stepButtonStyle))
            {
                ExecuteStep2_BuildHotDll();
            }

            GUILayout.Space(8);

            // 生成强更包（右边，红色）
            GUI.backgroundColor = Color.red;
            if (GUILayout.Button("🔨 生成强更包", stepButtonStyle))
            {
                ExecuteStep1_BuildBaseDll();
            }

            GUILayout.EndHorizontal();

            GUILayout.Space(8);

            // 构建资源
            GUI.backgroundColor = stepButtonColor;
            if (GUILayout.Button("📦 构建资源", stepButtonStyle))
            {
                ExecuteStep3_BuildAssets();
            }

            GUILayout.Space(8);

            // 提交资源
            if (GUILayout.Button("📤 提交资源", stepButtonStyle))
            {
                ExecuteSubmitAssets();
            }

            GUILayout.Space(8);

            // 生成AAB（仅Android平台显示）
            if (selectedPlatform == BuildTarget.Android)
            {
                if (GUILayout.Button("📱 生成AAB (含APK)", stepButtonStyle))
                {
                    ExecuteStep4_BuildAAB();
                }
            }
            else if (selectedPlatform == BuildTarget.iOS)
            {
                if (GUILayout.Button("🍎 构建iOS项目", stepButtonStyle))
                {
                    ExecuteStep4_BuildiOS();
                }
            }

            GUILayout.EndVertical();

            GUILayout.Space(15);

            // 信息提示
            var infoStyle = new GUIStyle(EditorStyles.helpBox)
            {
                padding = new RectOffset(10, 10, 8, 8)
            };

            GUILayout.BeginVertical(infoStyle);
            var tipStyle = new GUIStyle(EditorStyles.miniLabel)
            {
                wordWrap = true,
                normal = { textColor = new Color(0.7f, 0.7f, 0.7f, 1f) }
            };
            GUILayout.Label("💡 提示：请按顺序执行构建操作。版本号将在执行时自动更新到 GameConfig 和 PlayerSettings。", tipStyle);
            GUILayout.EndVertical();

            GUI.backgroundColor = originalColor;

            // 结束右侧设置区域
            GUILayout.EndVertical();

            // 结束主要内容区域的水平布局
            GUILayout.EndHorizontal();

            GUILayout.Space(10);
        }

        /// <summary>
        /// 获取选中版本数量
        /// </summary>
        private static int GetSelectedCount()
        {
            int count = 0;
            foreach (var item in versionItems)
            {
                if (item.isSelected)
                    count++;
            }
            return count;
        }

        /// <summary>
        /// 获取选中的版本号列表
        /// </summary>
        private static string[] GetSelectedVersions()
        {
            var selectedVersions = new List<string>();
            foreach (var item in versionItems)
            {
                if (item.isSelected && !string.IsNullOrEmpty(item.version))
                {
                    selectedVersions.Add(item.version);
                }
            }
            return selectedVersions.ToArray();
        }

        /// <summary>
        /// 添加版本号
        /// </summary>
        private static void AddVersion(string version)
        {
            if (string.IsNullOrEmpty(version))
                return;

            // 检查是否已存在
            foreach (var item in versionItems)
            {
                if (item.version == version)
                {
                    Debug.LogWarning($"版本号 {version} 已存在");
                    return;
                }
            }

            versionItems.Add(new VersionItem(version, true));
        }

        /// <summary>
        /// 检查版本号是否已存在
        /// </summary>
        private static bool VersionExists(string version)
        {
            foreach (var item in versionItems)
            {
                if (item.version == version)
                    return true;
            }
            return false;
        }

        /// <summary>
        /// 设置全选/取消全选
        /// </summary>
        private static void SetAllSelected(bool selected)
        {
            foreach (var item in versionItems)
            {
                item.isSelected = selected;
            }
        }

        /// <summary>
        /// 清空版本号列表
        /// </summary>
        private static void ClearVersions()
        {
            versionItems.Clear();
        }

        /// <summary>
        /// 绘制版本号列表管理区域
        /// </summary>
        private void DrawVersionListSection(GUIStyle containerStyle, GUIStyle labelStyle, GUIStyle textFieldStyle)
        {
            GUILayout.BeginVertical(containerStyle);

            // 标题栏
            GUILayout.BeginHorizontal();
            var titleStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontStyle = FontStyle.Bold,
                normal = { textColor = new Color(0.8f, 0.8f, 0.8f, 1f) }
            };

            showVersionListSection = EditorGUILayout.Foldout(showVersionListSection, "📋 版本号列表管理", titleStyle);

            // 显示选中版本数量
            int selectedCount = GetSelectedCount();
            int totalCount = versionItems.Count;

            var countStyle = new GUIStyle(EditorStyles.miniLabel)
            {
                normal = { textColor = Color.gray },
                alignment = TextAnchor.MiddleRight
            };
            GUILayout.Label($"({selectedCount}/{totalCount} 选中)", countStyle);

            GUILayout.EndHorizontal();

            if (showVersionListSection)
            {
                GUILayout.Space(8);

                // 添加新版本号区域
                GUILayout.BeginHorizontal();
                GUILayout.Label("🆕 添加版本号", labelStyle, GUILayout.Width(100));
                newVersionInput = EditorGUILayout.TextField(newVersionInput, textFieldStyle);

                GUI.backgroundColor = Color.green;
                if (GUILayout.Button("添加", GUILayout.Width(50)))
                {
                    if (!string.IsNullOrEmpty(newVersionInput.Trim()))
                    {
                        string trimmedVersion = newVersionInput.Trim();
                        if (!VersionExists(trimmedVersion))
                        {
                            AddVersion(trimmedVersion);
                            newVersionInput = "";
                            GUI.FocusControl(null); // 清除焦点
                        }
                        else
                        {
                            EditorUtility.DisplayDialog("版本号已存在", $"版本号 '{trimmedVersion}' 已存在！", "确定");
                        }
                    }
                }
                GUI.backgroundColor = Color.white;
                GUILayout.EndHorizontal();

                GUILayout.Space(8);

                // 操作按钮区域
                GUILayout.BeginHorizontal();

                GUI.backgroundColor = Color.cyan;
                if (GUILayout.Button("全选", GUILayout.Width(50)))
                {
                    SetAllSelected(true);
                }

                GUI.backgroundColor = Color.gray;
                if (GUILayout.Button("取消全选", GUILayout.Width(70)))
                {
                    SetAllSelected(false);
                }

                GUI.backgroundColor = Color.red;
                if (GUILayout.Button("清空列表", GUILayout.Width(70)))
                {
                    if (EditorUtility.DisplayDialog("确认清空", "确定要清空所有版本号吗？", "确定", "取消"))
                    {
                        ClearVersions();
                    }
                }

                GUI.backgroundColor = Color.white;
                GUILayout.FlexibleSpace();
                GUILayout.EndHorizontal();

                GUILayout.Space(8);

                // 版本号列表
                if (versionItems.Count > 0)
                {
                    var listStyle = new GUIStyle(EditorStyles.helpBox)
                    {
                        padding = new RectOffset(5, 5, 5, 5)
                    };

                    GUILayout.BeginVertical(listStyle);
                    versionListScrollPos = EditorGUILayout.BeginScrollView(versionListScrollPos, GUILayout.MaxHeight(150));

                    for (int i = 0; i < versionItems.Count; i++)
                    {
                        var item = versionItems[i];

                        GUILayout.BeginHorizontal();

                        // 复选框
                        bool newSelected = EditorGUILayout.Toggle(item.isSelected, GUILayout.Width(20));
                        if (newSelected != item.isSelected)
                        {
                            item.isSelected = newSelected;
                        }

                        // 版本号显示
                        var versionLabelStyle = new GUIStyle(EditorStyles.label)
                        {
                            normal = { textColor = item.isSelected ? Color.white : Color.gray }
                        };
                        GUILayout.Label(item.version, versionLabelStyle);

                        GUILayout.FlexibleSpace();

                        // 删除按钮
                        GUI.backgroundColor = Color.red;
                        if (GUILayout.Button("✕", GUILayout.Width(25), GUILayout.Height(18)))
                        {
                            if (EditorUtility.DisplayDialog("确认删除", $"确定要删除版本号 '{item.version}' 吗？", "确定", "取消"))
                            {
                                versionItems.RemoveAt(i);
                                break; // 跳出循环，因为列表已改变
                            }
                        }
                        GUI.backgroundColor = Color.white;

                        GUILayout.EndHorizontal();

                        if (i < versionItems.Count - 1)
                        {
                            GUILayout.Space(2);
                        }
                    }

                    EditorGUILayout.EndScrollView();
                    GUILayout.EndVertical();
                }
                else
                {
                    var emptyStyle = new GUIStyle(EditorStyles.centeredGreyMiniLabel)
                    {
                        normal = { textColor = Color.gray }
                    };
                    GUILayout.Label("暂无版本号，请添加版本号", emptyStyle);
                }

                GUILayout.Space(5);

                // 提示信息
                var tipStyle = new GUIStyle(EditorStyles.miniLabel)
                {
                    wordWrap = true,
                    normal = { textColor = new Color(0.6f, 0.6f, 0.6f, 1f) }
                };
                GUILayout.Label("💡 提示：构建资源时会自动拷贝到所有选中的版本号目录中", tipStyle);
            }

            GUILayout.EndVertical();
        }

        /// <summary>
        /// 生成整包DLL
        /// </summary>
        private void ExecuteStep1_BuildBaseDll()
        {
            if (!ValidateVersionInput()) return;

            try
            {
                Debug.Log("🔨 [整包DLL] 开始生成整包DLL...");
                if (EditorUtility.DisplayDialog("确认构建", "确定要构建整包吗？", "确定", "取消"))
                {
                    // 更新版本信息
                    UpdateVersionSettings();

                    // 调用BuildHotDll.BuildBaseDll方法
                    BuildHotDll.BuildBaseDll();

                    Debug.Log("✅ [整包DLL] 整包DLL生成完成");
                    EditorUtility.DisplayDialog("✅ 构建完成", "整包DLL生成完成！", "确定");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ [整包DLL] 整包DLL生成失败: {e.Message}");
                EditorUtility.DisplayDialog("❌ 构建失败", $"整包DLL生成失败:\n\n{e.Message}", "确定");
            }
        }

        /// <summary>
        /// 生成热更DLL
        /// </summary>
        private void ExecuteStep2_BuildHotDll()
        {
            if (!ValidateVersionInput()) return;

            try
            {
                Debug.Log("🔥 [热更DLL] 开始生成热更DLL...");

                // 更新版本信息
                UpdateVersionSettings();

                // 调用BuildHotDll.BuildDll方法（这是BuildjDll的实际方法名）
                BuildHotDll.BuildDll();

                Debug.Log("✅ [热更DLL] 热更DLL生成完成");
                EditorUtility.DisplayDialog("✅ 构建完成", "热更DLL生成完成！", "确定");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ [热更DLL] 热更DLL生成失败: {e.Message}");
                EditorUtility.DisplayDialog("❌ 构建失败", $"热更DLL生成失败:\n\n{e.Message}", "确定");
            }
        }

        /// <summary>
        /// 构建资源
        /// </summary>
        private void ExecuteStep3_BuildAssets()
        {
            if (!ValidateVersionInput()) return;

            try
            {
                Debug.Log("📦 [资源构建] 开始构建资源...");

                // 更新版本信息
                UpdateVersionSettings();

                // 调用YooAssetsBuilder.BuildBundles方法
                YooAssetsBuilder.BuildBundles(version);

                // 检查是否需要拷贝到多个版本号目录
                string[] selectedVersions = GetSelectedVersions();
                if (selectedVersions.Length > 0)
                {
                    Debug.Log($"📦 [多版本拷贝] 开始拷贝到 {selectedVersions.Length} 个版本号目录...");

                    // 获取构建版本号
                    string buildVersion = $"{version}.{ResVersion.version}";

                    // 执行多版本拷贝
                    YooAssetsBuilder.Copy2CDNDirectionMultiVersion(buildVersion, selectedVersions);
                }

                Debug.Log("✅ [资源构建] 资源构建完成");
                EditorUtility.DisplayDialog("✅ 构建完成", "资源构建完成！", "确定");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ [资源构建] 资源构建失败: {e.Message}");
                EditorUtility.DisplayDialog("❌ 构建失败", $"资源构建失败:\n\n{e.Message}", "确定");
            }
        }

        /// <summary>
        /// 提交资源到CDN
        /// </summary>
        private void ExecuteSubmitAssets()
        {
            try
            {
                Debug.Log("📤 [提交资源] 开始提交资源到CDN...");

                // 调用YooAssetsBuilder.Update2CDN方法
                YooAssetsBuilder.Update2CDN();

                Debug.Log("✅ [提交资源] 资源提交完成");
                EditorUtility.DisplayDialog("✅ 提交完成", "资源已成功提交到CDN！", "确定");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ [提交资源] 资源提交失败: {e.Message}");
                EditorUtility.DisplayDialog("❌ 提交失败", $"资源提交失败:\n\n{e.Message}", "确定");
            }
        }

        /// <summary>
        /// 生成AAB（Android）
        /// </summary>
        private void ExecuteStep4_BuildAAB()
        {
            if (!ValidateVersionInput()) return;

            try
            {
                Debug.Log("📱 [AAB构建] 开始生成AAB...");

                // 更新版本信息
                UpdateVersionSettings();

                // 弹出文件夹选择对话框
                string selectedBuildPath = ShowBuildPathDialog(BuildTarget.Android, version);
                if (!string.IsNullOrEmpty(selectedBuildPath))
                {
                    // 开始构建项目
                    StartBuildProcess(BuildTarget.Android, version, versionCode, selectedBuildPath, true, true);
                }
                else
                {
                    Debug.Log("用户取消了构建路径选择");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ [AAB构建] AAB生成失败: {e.Message}");
                EditorUtility.DisplayDialog("❌ 构建失败", $"AAB生成失败:\n\n{e.Message}", "确定");
            }
        }

        /// <summary>
        /// 构建iOS项目
        /// </summary>
        private void ExecuteStep4_BuildiOS()
        {
            if (!ValidateVersionInput()) return;

            try
            {
                Debug.Log("🍎 [iOS构建] 开始构建iOS项目...");

                // 更新版本信息
                UpdateVersionSettings();

                // 弹出文件夹选择对话框
                string selectedBuildPath = ShowBuildPathDialog(BuildTarget.iOS, version);
                if (!string.IsNullOrEmpty(selectedBuildPath))
                {
                    // 开始构建项目
                    StartBuildProcess(BuildTarget.iOS, version, versionCode, selectedBuildPath, false, false);
                }
                else
                {
                    Debug.Log("用户取消了构建路径选择");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ [iOS构建] iOS项目构建失败: {e.Message}");
                EditorUtility.DisplayDialog("❌ 构建失败", $"iOS项目构建失败:\n\n{e.Message}", "确定");
            }
        }

        /// <summary>
        /// 验证版本输入
        /// </summary>
        private bool ValidateVersionInput()
        {
            if (string.IsNullOrEmpty(version))
            {
                EditorUtility.DisplayDialog("❌ 错误", "版本号不能为空！", "确定");
                return false;
            }

            if (versionCode <= 0)
            {
                EditorUtility.DisplayDialog("❌ 错误", "版本代码必须大于0！", "确定");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 更新版本设置
        /// </summary>
        private void UpdateVersionSettings()
        {
            // 保存到配置文件并同步到GameConfig和PlayerSettings
            SaveConfigData();

            Debug.Log($"📱 [版本更新] 版本号: {version}, 版本代码: {versionCode}, 平台: {selectedPlatform}");
        }

        /// <summary>
        /// 显示构建路径选择对话框
        /// </summary>
        private static string ShowBuildPathDialog(BuildTarget buildTarget, string version)
        {
            string platformName = BuildVersionManager.GetPlatformDisplayName(buildTarget);
            string defaultPath = Path.GetFullPath("Release");

            // 确保默认路径存在
            if (!Directory.Exists(defaultPath))
            {
                Directory.CreateDirectory(defaultPath);
            }

            string title = $"选择 {platformName} 构建输出路径";
            string selectedPath = EditorUtility.OpenFolderPanel(title, defaultPath, "");

            if (!string.IsNullOrEmpty(selectedPath))
            {
                // 直接使用用户选择的路径作为构建路径
                Debug.Log($"📁 [路径选择] 用户选择路径: {selectedPath}");
                Debug.Log($"📁 [路径选择] 构建输出路径: {selectedPath}");

                return selectedPath;
            }

            return null;
        }

        /// <summary>
        /// 开始构建流程
        /// </summary>
        private static void StartBuildProcess(BuildTarget buildTarget, string version, int versionCode, string customBuildPath = null, bool buildAAB = false, bool buildApkFromAAB = false)
        {
            try
            {
                Debug.Log($"🚀 开始构建 {buildTarget} 项目...");
                Debug.Log($"📱 版本号: {version}");
                Debug.Log($"🔢 版本代码: {versionCode}");

                // 构建前检查
                if (!PreBuildValidation(buildTarget))
                {
                    Debug.LogError("❌ 构建前检查失败，构建已中止");
                    return;
                }

                // 检查并设置构建目标
                if (!BuildVersionManager.CheckAndSwitchBuildTarget(buildTarget))
                {
                    Debug.Log("用户取消了平台切换，构建已中止");
                    return;
                }

                // 获取构建场景
                string[] scenes = BuildVersionManager.GetBuildScenes();
                if (scenes.Length == 0)
                {
                    EditorUtility.DisplayDialog("❌ 构建错误", "没有找到可构建的场景！\n请在Build Settings中添加场景。", "确定");
                    return;
                }

                // 获取构建路径（使用自定义路径或默认路径）
                string buildPath = customBuildPath;

                // 获取构建目标组
                BuildTargetGroup targetGroup = BuildVersionManager.GetBuildTargetGroup(buildTarget);

                BuildOptions buildOptions = BuildOptions.None;
                buildOptions |= BuildOptions.AcceptExternalModificationsToPlayer;
                // buildOptions |= BuildOptions.CompressWithLz4;

                // 设置构建选项
                BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions
                {
                    scenes = scenes,
                    locationPathName = buildPath,
                    target = buildTarget,
                    targetGroup = targetGroup,
                    options = buildOptions
                };

                Debug.Log($"📋 [构建信息] 场景数量: {scenes.Length}");
                Debug.Log($"📋 [构建信息] 输出路径: {buildPath}");
                Debug.Log($"📋 [构建信息] 目标平台: {buildTarget}");
                Debug.Log($"📋 [构建信息] 目标组: {targetGroup}");
                Debug.Log($"📋 [构建信息] 构建选项: {buildPlayerOptions.options}");

                // 验证构建路径
                if (!ValidateBuildPath(buildPath, buildTarget))
                {
                    Debug.LogError("❌ 构建路径验证失败");
                    return;
                }

                // 执行构建
                Debug.Log("🔨 开始执行构建...");
                var report = BuildPipeline.BuildPlayer(buildPlayerOptions);

                // 处理构建结果
                BuildVersionManager.HandleBuildResult(report, buildTarget, version, versionCode, buildPath, buildAAB, buildApkFromAAB);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ 构建失败: {e.Message}");
                Debug.LogError($"❌ 堆栈跟踪: {e.StackTrace}");

                // 分析常见错误并提供解决方案
                string errorAnalysis = AnalyzeBuildError(e, buildTarget);

                string errorDetails = $"构建过程中发生错误:\n\n{e.Message}\n\n{errorAnalysis}";
                if (e.InnerException != null)
                {
                    errorDetails += $"\n\n内部错误: {e.InnerException.Message}";
                }

                EditorUtility.DisplayDialog("❌ 构建失败", errorDetails, "确定");
            }
        }

        /// <summary>
        /// 构建前验证
        /// </summary>
        private static bool PreBuildValidation(BuildTarget buildTarget)
        {
            Debug.Log("🔍 开始构建前检查...");

            // 检查是否有未保存的场景
            if (UnityEditor.SceneManagement.EditorSceneManager.GetActiveScene().isDirty)
            {
                bool saveScene = EditorUtility.DisplayDialog(
                    "⚠️ 未保存的场景",
                    "当前场景有未保存的更改。\n是否保存后继续构建？",
                    "保存并继续",
                    "取消构建"
                );

                if (saveScene)
                {
                    UnityEditor.SceneManagement.EditorSceneManager.SaveOpenScenes();
                    Debug.Log("✅ 场景已保存");
                }
                else
                {
                    Debug.Log("❌ 用户取消构建");
                    return false;
                }
            }

            // 检查是否有未保存的资源
            if (AssetDatabase.IsAssetImportWorkerProcess())
            {
                EditorUtility.DisplayDialog("⚠️ 资源导入中", "资源正在导入中，请等待完成后再构建。", "确定");
                return false;
            }

            // 保存所有资源
            AssetDatabase.SaveAssets();
            Debug.Log("✅ 资源已保存");
            return true;
        }


        /// <summary>
        /// 验证构建路径
        /// </summary>
        private static bool ValidateBuildPath(string buildPath, BuildTarget buildTarget)
        {
            try
            {
                Debug.Log($"🔍 [路径验证] 验证构建路径: {buildPath}");

                // 检查路径是否为空
                if (string.IsNullOrEmpty(buildPath))
                {
                    Debug.LogError("❌ [路径验证] 构建路径为空");
                    return false;
                }

                // 获取目录路径
                string directory = Path.GetDirectoryName(buildPath);

                // 检查目录是否存在，不存在则创建
                if (!Directory.Exists(directory))
                {
                    try
                    {
                        Directory.CreateDirectory(directory);
                        Debug.Log($"✅ [路径验证] 创建目录: {directory}");
                    }
                    catch (Exception e)
                    {
                        Debug.LogError($"❌ [路径验证] 无法创建目录 {directory}: {e.Message}");
                        return false;
                    }
                }

                // 检查路径长度（Windows路径限制）
                if (buildPath.Length > 260)
                {
                    Debug.LogError($"❌ [路径验证] 路径过长 ({buildPath.Length} 字符): {buildPath}");
                    return false;
                }

                // 检查路径中的非法字符
                char[] invalidChars = Path.GetInvalidPathChars();
                if (buildPath.IndexOfAny(invalidChars) >= 0)
                {
                    Debug.LogError($"❌ [路径验证] 路径包含非法字符: {buildPath}");
                    return false;
                }

                // 平台特定验证
                switch (buildTarget)
                {
                    case BuildTarget.Android:
                        return ValidateAndroidBuildPath(buildPath);
                    case BuildTarget.iOS:
                        return ValidateiOSBuildPath(buildPath);
                    default:
                        Debug.Log("✅ [路径验证] 通用路径验证完成");
                        return true;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"❌ [路径验证] 路径验证异常: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证Android构建路径
        /// </summary>
        private static bool ValidateAndroidBuildPath(string buildPath)
        {
            bool exportAsProject = EditorUserBuildSettings.exportAsGoogleAndroidProject;

            if (exportAsProject)
            {
                // 验证项目导出路径
                if (File.Exists(buildPath))
                {
                    Debug.LogError($"❌ [Android路径] 项目路径不能是文件: {buildPath}");
                    return false;
                }
                Debug.Log("✅ [Android路径] 项目导出路径验证完成");
            }
            else
            {
                // 验证APK文件路径
                if (!buildPath.EndsWith(".apk", StringComparison.OrdinalIgnoreCase))
                {
                    Debug.LogError($"❌ [Android路径] APK路径必须以.apk结尾: {buildPath}");
                    return false;
                }

                if (Directory.Exists(buildPath))
                {
                    Debug.LogError($"❌ [Android路径] APK路径不能是目录: {buildPath}");
                    return false;
                }
                Debug.Log("✅ [Android路径] APK文件路径验证完成");
            }

            return true;
        }

        /// <summary>
        /// 验证iOS构建路径
        /// </summary>
        private static bool ValidateiOSBuildPath(string buildPath)
        {
            // iOS总是导出项目
            if (File.Exists(buildPath))
            {
                Debug.LogError($"❌ [iOS路径] 项目路径不能是文件: {buildPath}");
                return false;
            }

            Debug.Log("✅ [iOS路径] 项目导出路径验证完成");
            return true;
        }

        /// <summary>
        /// 分析构建错误并提供解决方案
        /// </summary>
        private static string AnalyzeBuildError(Exception e, BuildTarget buildTarget)
        {
            string errorMessage = e.Message.ToLower();
            string stackTrace = e.StackTrace?.ToLower() ?? "";

            // 常见错误分析
            if (errorMessage.Contains("path") && errorMessage.Contains("invalid"))
            {
                return "💡 解决方案:\n• 检查构建路径是否包含特殊字符\n• 确保路径长度不超过260字符\n• 检查文件夹权限";
            }

            if (errorMessage.Contains("sdk") || errorMessage.Contains("android"))
            {
                return "💡 解决方案:\n• 检查Android SDK路径设置\n• 确保Android SDK版本兼容\n• 检查Build Tools是否安装";
            }

            if (errorMessage.Contains("keystore") || errorMessage.Contains("signing"))
            {
                return "💡 解决方案:\n• 检查密钥库文件路径\n• 验证密钥库密码\n• 确保签名配置正确";
            }

            if (errorMessage.Contains("gradle"))
            {
                return "💡 解决方案:\n• 清理Gradle缓存\n• 检查Gradle版本兼容性\n• 确保网络连接正常";
            }

            if (errorMessage.Contains("scene") || errorMessage.Contains("build settings"))
            {
                return "💡 解决方案:\n• 检查Build Settings中的场景列表\n• 确保所有场景文件存在\n• 重新添加场景到Build Settings";
            }

            if (errorMessage.Contains("il2cpp"))
            {
                return "💡 解决方案:\n• 检查IL2CPP设置\n• 确保C++编译器已安装\n• 检查脚本编译错误";
            }

            if (stackTrace.Contains("buildpipeline"))
            {
                return "💡 解决方案:\n• 检查构建参数设置\n• 确保目标平台配置正确\n• 尝试清理并重新构建";
            }

            // 平台特定错误
            switch (buildTarget)
            {
                case BuildTarget.Android:
                    return "💡 Android构建解决方案:\n• 检查Android SDK和NDK\n• 验证包名格式\n• 确保最小SDK版本设置正确";

                case BuildTarget.iOS:
                    return "💡 iOS构建解决方案:\n• 检查Xcode版本\n• 验证Bundle Identifier\n• 确保iOS版本设置正确";

                default:
                    return "💡 通用解决方案:\n• 检查Unity版本兼容性\n• 清理项目缓存\n• 重启Unity编辑器";
            }
        }
    }

    /// <summary>
    /// 版本号管理器
    /// </summary>
    public class BuildVersionManager
    {
        /// <summary>
        /// 更新GameConfig中的版本信息
        /// </summary>
        public static void UpdateGameConfigVersion(string version, int versionCode)
        {
            string configPath = "Assets/_MyGame/Scripts/GameConfig.cs";
            if (File.Exists(configPath))
            {
                string content = File.ReadAllText(configPath);

                // 更新VERSION
                content = System.Text.RegularExpressions.Regex.Replace(
                    content,
                    @"public static string VERSION = ""[^""]*"";",
                    $"public static string VERSION = \"{version}\";"
                );

                // 更新BUILD_VER
                content = System.Text.RegularExpressions.Regex.Replace(
                    content,
                    @"public static int BUILD_VER = \d+;",
                    $"public static int BUILD_VER = {versionCode};"
                );

                File.WriteAllText(configPath, content);
                AssetDatabase.Refresh();

                Debug.Log($"[版本设置] GameConfig已更新: VERSION={version}, BUILD_VER={versionCode}");
            }
        }

        /// <summary>
        /// 更新PlayerSettings中的版本信息
        /// </summary>
        public static void UpdatePlayerSettingsVersion(BuildTarget platform, string version, int versionCode)
        {
            // 设置应用版本号
            PlayerSettings.bundleVersion = version;

            if (platform == BuildTarget.Android)
            {
                PlayerSettings.Android.bundleVersionCode = versionCode;
                Debug.Log($"[版本设置] Android版本已设置: {version} ({versionCode})");
            }
            else if (platform == BuildTarget.iOS)
            {
                PlayerSettings.iOS.buildNumber = versionCode.ToString();
                Debug.Log($"[版本设置] iOS版本已设置: {version} ({versionCode})");
            }

            // 保存项目设置
            AssetDatabase.SaveAssets();
        }

        /// <summary>
        /// 检查并切换构建目标
        /// </summary>
        public static bool CheckAndSwitchBuildTarget(BuildTarget targetPlatform)
        {
            BuildTarget currentPlatform = EditorUserBuildSettings.activeBuildTarget;

            // 如果当前平台与目标平台一致，无需切换
            if (currentPlatform == targetPlatform)
            {
                Debug.Log($"✅ 当前平台已是 {targetPlatform}，无需切换");
                return true;
            }

            // 获取平台显示名称
            string currentPlatformName = GetPlatformDisplayName(currentPlatform);
            string targetPlatformName = GetPlatformDisplayName(targetPlatform);

            // 弹窗询问用户是否切换平台
            string message = $"🔄 平台切换确认\n\n" +
                           $"当前平台: {currentPlatformName}\n" +
                           $"目标平台: {targetPlatformName}\n\n" +
                           $"是否切换到目标平台？\n" +
                           $"(切换平台可能需要一些时间)";

            bool switchPlatform = EditorUtility.DisplayDialog(
                "🔄 平台切换确认",
                message,
                "切换",
                "取消"
            );

            if (switchPlatform)
            {
                Debug.Log($"🔄 正在切换平台: {currentPlatformName} → {targetPlatformName}");

                // 显示进度条
                EditorUtility.DisplayProgressBar("平台切换", $"正在切换到 {targetPlatformName}...", 0.5f);

                try
                {
                    // 执行平台切换
                    BuildTargetGroup targetGroup = GetBuildTargetGroup(targetPlatform);
                    bool success = EditorUserBuildSettings.SwitchActiveBuildTarget(targetGroup, targetPlatform);

                    if (success)
                    {
                        Debug.Log($"✅ 平台切换成功: {targetPlatformName}");
                        return true;
                    }
                    else
                    {
                        Debug.LogError($"❌ 平台切换失败: {targetPlatformName}");
                        EditorUtility.DisplayDialog("❌ 切换失败", $"无法切换到 {targetPlatformName} 平台", "确定");
                        return false;
                    }
                }
                finally
                {
                    EditorUtility.ClearProgressBar();
                }
            }
            else
            {
                Debug.Log("用户取消了平台切换");
                return false;
            }
        }

        /// <summary>
        /// 获取平台显示名称
        /// </summary>
        public static string GetPlatformDisplayName(BuildTarget buildTarget)
        {
            switch (buildTarget)
            {
                case BuildTarget.Android:
                    return "📱 Android";
                case BuildTarget.iOS:
                    return "🍎 iOS";
                case BuildTarget.StandaloneWindows:
                    return "🖥️ Windows";
                case BuildTarget.StandaloneWindows64:
                    return "🖥️ Windows 64-bit";
                case BuildTarget.StandaloneOSX:
                    return "🍎 macOS";
                case BuildTarget.WebGL:
                    return "🌐 WebGL";
                case BuildTarget.StandaloneLinux64:
                    return "🐧 Linux";
                default:
                    return buildTarget.ToString();
            }
        }

        /// <summary>
        /// 获取构建目标组
        /// </summary>
        public static BuildTargetGroup GetBuildTargetGroup(BuildTarget buildTarget)
        {
            switch (buildTarget)
            {
                case BuildTarget.Android:
                    return BuildTargetGroup.Android;
                case BuildTarget.iOS:
                    return BuildTargetGroup.iOS;
                case BuildTarget.StandaloneWindows:
                case BuildTarget.StandaloneWindows64:
                    return BuildTargetGroup.Standalone;
                case BuildTarget.WebGL:
                    return BuildTargetGroup.WebGL;
                default:
                    return BuildTargetGroup.Unknown;
            }
        }

        /// <summary>
        /// 获取构建场景列表
        /// </summary>
        public static string[] GetBuildScenes()
        {
            var scenes = new List<string>();
            foreach (var scene in EditorBuildSettings.scenes)
            {
                if (scene.enabled)
                {
                    scenes.Add(scene.path);
                }
            }
            return scenes.ToArray();
        }

        /// <summary>
        /// 处理构建结果
        /// </summary>
        public static void HandleBuildResult(UnityEditor.Build.Reporting.BuildReport report, BuildTarget buildTarget, string version, int versionCode, string buildPath = null, bool buildAAB = false, bool buildApkFromAAB = false)
        {
            var summary = report.summary;

            if (summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
            {
                Debug.Log($"✅ 构建成功！");
                Debug.Log($"📁 输出路径: {summary.outputPath}");
                Debug.Log($"⏱️ 构建时间: {summary.totalTime}");
                Debug.Log($"📦 构建大小: {summary.totalSize} bytes");

                // 如果是Android平台且需要构建AAB
                if (buildTarget == BuildTarget.Android && buildAAB)
                {
                    ExecuteAABBuild(buildPath ?? Path.GetDirectoryName(summary.outputPath), version, versionCode, buildApkFromAAB);
                }

                string message = $"🎉 {buildTarget} 项目构建成功！\n\n" +
                               $"📱 版本号: {version}\n" +
                               $"🔢 版本代码: {versionCode}\n" +
                               $"📁 输出路径: {summary.outputPath}\n" +
                               $"⏱️ 构建时间: {summary.totalTime}\n" +
                               $"📦 构建大小: {FormatBytes(summary.totalSize)}";

                if (buildTarget == BuildTarget.Android && buildAAB)
                {
                    message += "\n\n📱 AAB构建已启动，请查看控制台输出";
                }

                if (EditorUtility.DisplayDialog("✅ 构建成功", message, "打开文件夹", "确定"))
                {
                    if (buildTarget == BuildTarget.Android && buildAAB)
                    {
                        string aabDirectory = Path.Combine(buildPath, "launcher", "build", "outputs", "bundle", "release");
                        if (Directory.Exists(aabDirectory))
                        {
                            EditorUtility.RevealInFinder(aabDirectory);
                        }
                        else
                        {
                            EditorUtility.RevealInFinder(summary.outputPath);
                        }
                    }
                    else
                    {
                        EditorUtility.RevealInFinder(summary.outputPath);
                    }
                }
            }
            else
            {
                Debug.LogError($"❌ 构建失败: {summary.result}");

                string errorMessage = $"❌ {buildTarget} 项目构建失败！\n\n" +
                                     $"错误类型: {summary.result}\n" +
                                     $"构建时间: {summary.totalTime}";

                EditorUtility.DisplayDialog("❌ 构建失败", errorMessage, "确定");
            }
        }

        // [MenuItem("Build/TestAab", priority = 13)]
        // private static void TestAab()
        // {
        //     var  buildPath = "E:\\ProjectsUnity\\BoBoSurviveClient-TW-Android\\Release\\BoBoSurviveClient-TW-AndrodStudio2";
        //     ExecuteAABBuild(buildPath, "1.0.0", 1, true);
        // }
        /// <summary>
        /// 执行AAB构建
        /// </summary>
        private static void ExecuteAABBuild(string buildPath, string version, int versionCode, bool buildApkFromAAB)
        {
            try
            {
                // 根据操作系统选择相应的构建脚本
                string scriptFileName;

                if (Application.platform == RuntimePlatform.WindowsEditor)
                {
                    scriptFileName = "build_aab.bat";
                }
                else if (Application.platform == RuntimePlatform.OSXEditor)
                {
                    scriptFileName = "build_aab.sh";
                }
                else
                {
                    Debug.LogError($"❌ [AAB构建] 不支持的操作系统: {Application.platform}");
                    return;
                }

                string scriptFilePath = Path.Combine(buildPath, scriptFileName);

                if (File.Exists(scriptFilePath))
                {
                    Debug.Log($"📱 [AAB构建] 找到AAB构建脚本: {scriptFilePath}");
                    Debug.Log($"📱 [AAB构建] 开始执行AAB构建...");

                    System.Diagnostics.ProcessStartInfo processInfo;

                    if (Application.platform == RuntimePlatform.WindowsEditor)
                    {
                        // Windows: 直接执行bat文件
                        processInfo = new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = scriptFilePath,
                            WorkingDirectory = buildPath,
                            UseShellExecute = true,
                            CreateNoWindow = false
                        };
                    }
                    else
                    {
                        // Mac/Linux: 直接执行shell脚本，不使用终端窗口
                        processInfo = new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = "bash",
                            Arguments = $"\"{scriptFilePath}\"",
                            WorkingDirectory = buildPath,
                            UseShellExecute = false,
                            CreateNoWindow = true,
                            RedirectStandardOutput = true,
                            RedirectStandardError = true
                        };
                    }

                    var process = System.Diagnostics.Process.Start(processInfo);

                    if (process != null)
                    {
                        if (Application.platform == RuntimePlatform.WindowsEditor)
                        {
                            Debug.Log($"📱 [AAB构建] AAB构建脚本已在新的终端窗口中启动，等待完成...");

                            // Windows: 等待进程完成
                            process.WaitForExit();

                            if (process.ExitCode == 0)
                            {
                                Debug.Log($"✅ [AAB构建] AAB构建脚本执行成功");
                                // 查找生成的AAB文件并重命名
                                string aabPath = RenameAABFile(buildPath, version, versionCode);

                                if (buildApkFromAAB && !string.IsNullOrEmpty(aabPath))
                                {
                                    ConvertAabToApk(aabPath, version, versionCode);
                                }
                            }
                            else
                            {
                                Debug.LogError($"❌ [AAB构建] AAB构建脚本执行失败，退出代码: {process.ExitCode}");
                                EditorUtility.DisplayDialog("❌ AAB构建失败",
                                    $"AAB构建脚本执行失败，退出代码: {process.ExitCode}\n请检查弹出的终端窗口中的错误信息。",
                                    "确定");
                            }
                        }
                        else
                        {
                            Debug.Log($"📱 [AAB构建] AAB构建脚本已启动，等待完成...");

                            // Mac: 等待进程完成
                            string output = process.StandardOutput.ReadToEnd();
                            string error = process.StandardError.ReadToEnd();
                            process.WaitForExit();

                            if (process.ExitCode == 0)
                            {
                                Debug.Log($"✅ [AAB构建] AAB构建脚本执行成功");
                                if (!string.IsNullOrEmpty(output))
                                {
                                    Debug.Log($"[AAB构建] 输出: {output}");
                                }

                                // 查找生成的AAB文件并重命名
                                string aabPath = RenameAABFile(buildPath, version, versionCode);

                                if (buildApkFromAAB && !string.IsNullOrEmpty(aabPath))
                                {
                                    ConvertAabToApk(aabPath, version, versionCode);
                                }
                            }
                            else
                            {
                                Debug.LogError($"❌ [AAB构建] AAB构建脚本执行失败，退出代码: {process.ExitCode}");
                                if (!string.IsNullOrEmpty(error))
                                {
                                    Debug.LogError($"[AAB构建] 错误信息: {error}");
                                }
                                EditorUtility.DisplayDialog("❌ AAB构建失败",
                                    $"AAB构建脚本执行失败，退出代码: {process.ExitCode}\n\n错误信息:\n{error}",
                                    "确定");
                            }
                        }

                        process.Close();
                    }
                }
                else
                {
                    Debug.LogWarning($"⚠️ [AAB构建] 未找到AAB构建脚本: {scriptFilePath}");

                    // 如果是Mac系统且没有找到shell脚本，提示用户
                    if (Application.platform == RuntimePlatform.OSXEditor)
                    {
                        EditorUtility.DisplayDialog("⚠️ 缺少构建脚本",
                            $"未找到Mac版本的AAB构建脚本: {scriptFileName}\n\n" +
                            "请确保在Android项目目录中存在build_aab.sh文件。\n" +
                            "该脚本用于执行Gradle构建AAB，构建完成后会自动进行AAB重命名和APK导出。",
                            "确定");
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ [AAB构建] AAB构建失败: {e.Message}");
            }
        }

        /// <summary>
        /// 读取gd_config.xml中的platform值
        /// </summary>
        private static string GetPlatformFromConfig()
        {
            try
            {
                string configPath = Path.Combine(Application.dataPath, "..", "Release", "BoBoSurviveClient-TW-AndroidStudio", "unityLibrary", "src", "main", "res", "values", "gd_config.xml");
                configPath = Path.GetFullPath(configPath);
                
                if (!File.Exists(configPath))
                {
                    Debug.LogWarning($"⚠️ [平台配置] gd_config.xml文件不存在: {configPath}");
                    return "Unknown";
                }

                string xmlContent = File.ReadAllText(configPath);
                
                // 使用简单的字符串匹配来提取platform值
                var match = System.Text.RegularExpressions.Regex.Match(xmlContent, @"<string name=""gd_platform"">([^<]+)</string>");
                if (match.Success)
                {
                    string platform = match.Groups[1].Value.Trim();
                    Debug.Log($"📱 [平台配置] 从gd_config.xml读取到platform: {platform}");
                    return platform;
                }
                else
                {
                    Debug.LogWarning("⚠️ [平台配置] 在gd_config.xml中未找到gd_platform配置");
                    return "Unknown";
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ [平台配置] 读取gd_config.xml失败: {e.Message}");
                return "Unknown";
            }
        }

        /// <summary>
        /// 重命名AAB文件
        /// </summary>
        private static string RenameAABFile(string buildPath, string version, int versionCode)
        {
            try
            {
                // AAB文件所在的具体路径
                string aabDirectory = Path.Combine(buildPath, "launcher", "build", "outputs", "bundle", "release");
                Debug.Log($"📱 [AAB重命名] 查找AAB文件路径: {aabDirectory}");

                if (!Directory.Exists(aabDirectory))
                {
                    Debug.LogWarning($"⚠️ [AAB重命名] AAB目录不存在: {aabDirectory}");
                    return null;
                }

                string originalAabPath = Path.Combine(aabDirectory, "launcher-release.aab");
                if (!File.Exists(originalAabPath))
                {
                    Debug.LogWarning($"⚠️ [AAB重命名] 未找到目标文件: {originalAabPath}");
                    return null;
                }

                // 获取平台信息
                string platform = GetPlatformFromConfig();
                
                // 生成新的文件名格式：launcher-release_{platform}_{version}_{versionCode}_{ResVersion.version}.aab
                string newFileName = $"launcher-release_{platform}_{version}_{versionCode}_{ResVersion.version}.aab";
                string newFilePath = Path.Combine(aabDirectory, newFileName);

                // 如果目标文件已存在，先删除
                if (File.Exists(newFilePath))
                {
                    File.Delete(newFilePath);
                    Debug.Log($"📱 [AAB重命名] 删除已存在的文件: {newFilePath}");
                }

                // 重命名文件
                File.Move(originalAabPath, newFilePath);
                Debug.Log($"✅ [AAB重命名] AAB文件重命名成功:");
                Debug.Log($"   原文件: {originalAabPath}");
                Debug.Log($"   新文件: {newFilePath}");
                return newFilePath;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ [AAB重命名] 重命名AAB文件失败: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// 将AAB转换为APK
        /// </summary>
        private static void ConvertAabToApk(string aabPath, string version, int versionCode)
        {
            Debug.Log($"📱 [AAB转APK] 开始将 {Path.GetFileName(aabPath)} 转换为APK...");

            try
            {
                // 假设bundletool.jar在项目根目录的 "Tools" 文件夹下
                string bundletoolPath = Path.GetFullPath("Tools/bundletool.jar");
                if (!File.Exists(bundletoolPath))
                {
                    EditorUtility.DisplayDialog("❌ 错误", "未找到 bundletool.jar\n请将其放置在项目根目录的 'Tools' 文件夹下。", "确定");
                    Debug.LogError($"❌ [AAB转APK] 未找到 bundletool.jar，路径: {bundletoolPath}");
                    return;
                }

                // keystore信息从PlayerSettings获取
                string keystorePath = Path.GetFullPath(PlayerSettings.Android.keystoreName);
                string keystorePass = PlayerSettings.Android.keystorePass;
                string keyAlias = PlayerSettings.Android.keyaliasName;
                string keyAliasPass = PlayerSettings.Android.keyaliasPass;

                if (string.IsNullOrEmpty(keystorePath) || !File.Exists(keystorePath))
                {
                    EditorUtility.DisplayDialog("❌ 错误", "未配置或找不到Keystore文件。\n请在 PlayerSettings > Publishing Settings 中配置。", "确定");
                    Debug.LogError("❌ [AAB转APK] Keystore未配置或找不到。");
                    return;
                }

                string outputApksPath = Path.ChangeExtension(aabPath, ".apks");
                string platform = GetPlatformFromConfig();
                string finalApkPath = Path.Combine(Path.GetDirectoryName(aabPath), $"universal_{platform}_{version}_{versionCode}_{ResVersion.version}.apk");

                // 构造bundletool命令
                string arguments = $"build-apks --bundle=\"{aabPath}\" --output=\"{outputApksPath}\" " +
                                   $"--mode=universal " +
                                   $"--ks=\"{keystorePath}\" --ks-pass=pass:{keystorePass} " +
                                   $"--ks-key-alias=\"{keyAlias}\" --key-pass=pass:{keyAliasPass}";

                Debug.Log($"[AAB转APK] 执行命令: java -jar \"{bundletoolPath}\" {arguments}");

                var processInfo = new System.Diagnostics.ProcessStartInfo("java", $"-jar \"{bundletoolPath}\" {arguments}")
                {
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    WorkingDirectory = Application.dataPath
                };

                using (var process = System.Diagnostics.Process.Start(processInfo))
                {
                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        Debug.Log($"✅ [AAB转APK] .apks文件生成成功: {outputApksPath}");
                        Debug.Log(output);

                        // 从.apks中提取universal.apk
                        // .apks文件本质上是一个zip文件
                        using (ZipArchive archive = ZipFile.OpenRead(outputApksPath))
                        {
                            var universalApkEntry = archive.GetEntry("universal.apk");
                            if (universalApkEntry != null)
                            {
                                // 提取并重命名
                                universalApkEntry.ExtractToFile(finalApkPath, true);
                                Debug.Log($"✅ [AAB转APK] 成功提取 universal.apk 到: {finalApkPath}");
                            }
                            else
                            {
                                Debug.LogError("❌ [AAB转APK] 在生成的.apks文件中未找到 universal.apk");
                            }
                        }

                        // 删除临时的.apks文件
                        File.Delete(outputApksPath);
                    }
                    else
                    {
                        Debug.LogError($"❌ [AAB转APK] bundletool执行失败，退出代码: {process.ExitCode}");
                        Debug.LogError($"错误信息: {error}");
                        EditorUtility.DisplayDialog("❌ AAB转APK失败", $"bundletool执行失败，请查看控制台日志获取详细错误信息。", "确定");
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"❌ [AAB转APK] 转换过程中发生异常: {e.Message}");
                Debug.LogError($"堆栈跟踪: {e.StackTrace}");
            }
        }

        /// <summary>
        /// 格式化字节大小
        /// </summary>
        public static string FormatBytes(ulong bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = (decimal)bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            return string.Format("{0:n1} {1}", number, suffixes[counter]);
        }
    }
}
