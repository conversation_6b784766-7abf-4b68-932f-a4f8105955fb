#if UNITY_EDITOR
using System;
using System.IO;
using UnityEditor;
using UnityEngine;
using YooAsset;
using YooAsset.Editor;

public class YooAssetsBuilder
{
    private static string cdnDir = "yrkp_tw";
    private static string bundleVersion;

    [MenuItem("Build/BuildTestBundles", priority = 0)]
    private static void BuildTestBundles()
    {
        bundleVersion = GetBuildPackageVersion();
        var isSuccess = BuildBundles(bundleVersion, false);
        if (!isSuccess)
            return;

        BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
        var targetDir = GetTestBundlesTargetDir(buildTarget);
        Copy2CDNDirection(bundleVersion, targetDir);
    }
    [MenuItem("Build/BuildTestBundlesUpdate", priority = 0)]
    private static void BuildTestBundlesUpdate()
    {
        bundleVersion = GetBuildPackageVersion();
        var isSuccess = BuildBundles(bundleVersion, true);
        if (!isSuccess)
            return;

        BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
        var targetDir = GetTestBundlesTargetDir(buildTarget);
        Copy2CDNDirection(bundleVersion, targetDir);
    }


    [MenuItem("Build/BuildBundles", priority = 0)]
    public static void BuildBundles()
    {
        BuildBundles(GameConfig.GetVer());
    }

    public static bool BuildBundles(string version)
    {
        Log.Info("BuildBundles ver:" + version);
        bundleVersion = GetBuildPackageVersion();
        var isSuccess = BuildBundles(bundleVersion, false);
        if (!isSuccess)
            return false;

        BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
        var targetDir = Path.Join(Application.dataPath, "../../WebSite/" + cdnDir + "/" + buildTarget.ToString(), version + "/StreamingAssets/yoo/DefaultPackage");
        Copy2CDNDirection(bundleVersion, targetDir);
        return true;
    }

    [MenuItem("Build/BuildBundlesUpdate", priority = 0)]
    public static void BuildBundlesUpdate()
    {
        bundleVersion = GetBuildPackageVersion();
        var isSuccess = BuildBundles(bundleVersion, true);
        if (!isSuccess)
            return;

        BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
        var targetDir = Path.Join(Application.dataPath, "../../WebSite/" + cdnDir + "/" + buildTarget.ToString(), GameConfig.GetVer() + "/StreamingAssets/yoo/DefaultPackage");
        Copy2CDNDirection(bundleVersion, targetDir);
    }

    private static void Copy2CDNDirection(string version, string targetDir)
    {
        string defaultOutputRoot = AssetBundleBuilderHelper.GetDefaultBuildOutputRoot();
        BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
        var bundleRootDir = Path.Join(defaultOutputRoot, buildTarget.ToString());
        var bundleDir = Path.Join(bundleRootDir, "DefaultPackage", version);

        FileUtils.CopyDirectory(bundleDir, targetDir);
        Debug.Log($"拷贝成功:" + bundleDir + " to " + targetDir);
    }

    /// <summary>
    /// 拷贝构建产物到多个版本号目录
    /// </summary>
    /// <param name="sourceVersion">源版本号（构建产物的版本号）</param>
    /// <param name="targetVersions">目标版本号列表</param>
    public static void Copy2CDNDirectionMultiVersion(string sourceVersion, string[] targetVersions)
    {
        if (targetVersions == null || targetVersions.Length == 0)
        {
            Debug.LogWarning("目标版本号列表为空，跳过拷贝操作");
            return;
        }

        string defaultOutputRoot = AssetBundleBuilderHelper.GetDefaultBuildOutputRoot();
        BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
        var bundleRootDir = Path.Join(defaultOutputRoot, buildTarget.ToString());
        var sourceBundleDir = Path.Join(bundleRootDir, "DefaultPackage", sourceVersion);

        if (!Directory.Exists(sourceBundleDir))
        {
            Debug.LogError($"源构建目录不存在: {sourceBundleDir}");
            return;
        }

        int successCount = 0;
        int totalCount = targetVersions.Length;

        var copyVersionStr = "";
        foreach (string targetVersion in targetVersions)
        {
            if (string.IsNullOrEmpty(targetVersion))
            {
                Debug.LogWarning("跳过空的目标版本号");
                continue;
            }

            try
            {
                var targetDir = Path.Join(Application.dataPath, "../../WebSite/" + cdnDir + "/" + buildTarget.ToString(), targetVersion + "/StreamingAssets/yoo/DefaultPackage");

                // 确保目标目录存在
                Directory.CreateDirectory(Path.GetDirectoryName(targetDir));

                FileUtils.CopyDirectory(sourceBundleDir, targetDir);
                copyVersionStr += $"{targetVersion}  ";
                Debug.Log($"✅ 拷贝成功: {sourceVersion} -> {targetVersion}");
                successCount++;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ 拷贝到版本 {targetVersion} 失败: {e.Message}");
            }
        }

        Debug.Log($"📦 多版本拷贝完成: {copyVersionStr}");
    }

    [MenuItem("Build/Update2CDN", priority = 0)]
    public static void Update2CDN()
    {
        var websiteDir = Path.Join(Application.dataPath, "../../WebSite", cdnDir);

        // 根据操作系统选择正确的 git 命令
        string gitCommand = GetGitCommand();

        var processInfo = new System.Diagnostics.ProcessStartInfo(gitCommand, $"-C \"{websiteDir}\" pull --rebase");
        var process = System.Diagnostics.Process.Start(processInfo);
        process.WaitForExit();
        processInfo = new System.Diagnostics.ProcessStartInfo(gitCommand, $"-C \"{websiteDir}\" add -A");
        process = System.Diagnostics.Process.Start(processInfo);
        process.WaitForExit();
        string platfrom = "(android)";
#if UNITY_IOS
        platfrom = "(ios)";
#endif
        processInfo = new System.Diagnostics.ProcessStartInfo(gitCommand, $"-C \"{websiteDir}\" commit -m \"Auto commit ==> {cdnDir}-{GameConfig.GetVer()}.{ResVersion.version} {platfrom}\"");
        process = System.Diagnostics.Process.Start(processInfo);
        process.WaitForExit();
        processInfo = new System.Diagnostics.ProcessStartInfo(gitCommand, $"-C \"{websiteDir}\" push");
        process = System.Diagnostics.Process.Start(processInfo);
        process.WaitForExit();

        Debug.Log($"提交成功：" + websiteDir);

        // if (string.IsNullOrEmpty(bundleVersion))
        // {
        //     Debug.Log($"请先执行BuildBundles， 再执行Update2CDN");
        //     return;
        // }
        // string batFilePath = Path.Combine(defaultOutputRoot, "uploadCDN_wxgame.bat");
        // if (File.Exists(batFilePath))
        // {
        //     var startInfo = new System.Diagnostics.ProcessStartInfo(batFilePath, GameConfig.GetVer());
        //     startInfo.WorkingDirectory = defaultOutputRoot;
        //     var process = new System.Diagnostics.Process();
        //     process.StartInfo = startInfo;
        //     process.Start();
        // }
        // else
        // {
        //     Debug.LogError("uploadCDN_wxgame.bat file not found in bundleRootDir");
        // }
    }

    [MenuItem("Tools/ClearBundleCache", priority = 11)]
    private static void ClearBundleCache()
    {
       var package = YooAssets.GetPackage("DefaultPackage");
        package?.ClearPackageSandbox();
    }

    public static bool BuildBundles(string packageVersion, bool isUpdate)
    {
        BuildTarget buildTarget = UnityEditor.EditorUserBuildSettings.activeBuildTarget;
        Debug.Log($"开始构建Bundles : {buildTarget}_{packageVersion}");

        // 构建参数
        string defaultOutputRoot = AssetBundleBuilderHelper.GetDefaultBuildOutputRoot();
        BuildParameters buildParameters = new BuildParameters();
        buildParameters.StreamingAssetsRoot = AssetBundleBuilderHelper.GetDefaultStreamingAssetsRoot();
        buildParameters.BuildOutputRoot = defaultOutputRoot;
        buildParameters.BuildTarget = buildTarget;
        buildParameters.BuildPipeline = AssetBundleBuilderSettingData.Setting.BuildPipeline;
        buildParameters.BuildMode = EBuildMode.IncrementalBuild;
        buildParameters.PackageName = AssetBundleBuilderSettingData.Setting.BuildPackage;
        buildParameters.PackageVersion = packageVersion;
        buildParameters.VerifyBuildingResult = true;
        buildParameters.SharedPackRule = new ZeroRedundancySharedPackRule();
        buildParameters.EncryptionServices = CreateEncryptionServicesInstance(AssetBundleBuilderSettingData.Setting.EncyptionClassName);
        buildParameters.CompressOption = AssetBundleBuilderSettingData.Setting.CompressOption;
        buildParameters.OutputNameStyle = AssetBundleBuilderSettingData.Setting.OutputNameStyle;
        buildParameters.CopyBuildinFileOption = isUpdate ? ECopyBuildinFileOption.None : ECopyBuildinFileOption.ClearAndCopyAll;
        buildParameters.CopyBuildinFileTags = AssetBundleBuilderSettingData.Setting.CopyBuildinFileTags;

        // 执行构建
        AssetBundleBuilder builder = new AssetBundleBuilder();
        var buildResult = builder.Run(buildParameters);
        if (buildResult.Success)
        {
            Debug.Log($"构建成功 : {buildResult.OutputPackageDirectory}");
            return true;
        }
        else
        {
            Debug.LogError($"构建失败 : {buildResult.ErrorInfo}");
        }
        return false;
    }

    private static string GetBuildPackageVersion()
    {
        return $"{GameConfig.GetVer()}.{ResVersion.version}";
        // return DateTime.Now.ToString("yyyyMMdd_HHmmss");
    }

    /// <summary>
    /// 获取测试包的目标目录
    /// </summary>
    /// <param name="buildTarget">构建目标平台</param>
    /// <returns>目标目录路径</returns>
    private static string GetTestBundlesTargetDir(BuildTarget buildTarget)
    {
        
        return Path.Join(Application.dataPath, "../Bundles/LocalCDN/" + buildTarget.ToString(), GameConfig.GetVer() + "/StreamingAssets/yoo/DefaultPackage");
        
    }

    /// <summary>
    /// 检查指定路径是否有写入权限
    /// </summary>
    /// <param name="path">要检查的路径</param>
    /// <returns>如果有写入权限返回 true，否则返回 false</returns>
    private static bool HasWritePermission(string path)
    {
        try
        {
            // 如果目录不存在，尝试创建它来测试权限
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
                return true;
            }

            // 如果目录存在，尝试创建一个临时文件来测试写入权限
            var testFile = Path.Combine(path, "test_write_permission.tmp");
            File.WriteAllText(testFile, "test");
            File.Delete(testFile);
            return true;
        }
        catch (UnauthorizedAccessException)
        {
            return false;
        }
        catch (IOException)
        {
            return false;
        }
        catch (System.Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 检测当前是否运行在 macOS 系统上
    /// </summary>
    /// <returns>如果是 macOS 返回 true，否则返回 false</returns>
    private static bool IsMacOS()
    {
#if UNITY_EDITOR_OSX
        return true;
#else
        return false;
#endif
    }

    /// <summary>
    /// 根据操作系统获取正确的 git 命令
    /// </summary>
    /// <returns>Windows 返回 "git.exe"，Mac/Linux 返回 "git"</returns>
    private static string GetGitCommand()
    {
#if UNITY_EDITOR_WIN
        return "git.exe";
#else
        return "git";
#endif
    }

    private static IEncryptionServices CreateEncryptionServicesInstance(string encyptionClassName)
    {
        var encryptionClassTypes = EditorTools.GetAssignableTypes(typeof(IEncryptionServices));
        for (int i = 0; i < encryptionClassTypes.Count; i++)
        {
            var classType = encryptionClassTypes[i];
            if (classType.Name == encyptionClassName)
            {
                return (IEncryptionServices)Activator.CreateInstance(classType);
            }
        }
        return null;
    }
}

#endif