using UnityEngine;
using UnityEditor;

namespace WGame.Editor
{
    /// <summary>
    /// 版本号管理功能测试
    /// </summary>
    public static class BuildVersionTest
    {
        [MenuItem("Build/Test Version Config", priority = 100)]
        public static void TestVersionConfig()
        {
            Debug.Log("=== 测试版本号配置功能 ===");
            Debug.Log("请打开 Build -> BuildWindow 来测试版本号列表管理功能");
            Debug.Log("版本号列表管理功能已集成到BuildWindow中");
        }
    }
}
