using UnityEngine;
using UnityEditor;

namespace WGame.Editor
{
    /// <summary>
    /// 版本号管理功能测试
    /// </summary>
    public static class BuildVersionTest
    {
        [MenuItem("Build/Test Version Config", priority = 100)]
        public static void TestVersionConfig()
        {
            Debug.Log("=== 测试版本号配置功能 ===");
            
            // 测试添加版本号
            BuildVersionConfigManager.AddVersion("1.0.0");
            BuildVersionConfigManager.AddVersion("1.0.1");
            BuildVersionConfigManager.AddVersion("1.1.0");
            
            Debug.Log($"添加版本号后，总数: {BuildVersionConfigManager.Config.VersionItems.Count}");
            
            // 测试获取选中版本
            string[] selectedVersions = BuildVersionConfigManager.GetSelectedVersions();
            Debug.Log($"选中的版本数量: {selectedVersions.Length}");
            Debug.Log($"选中的版本: [{string.Join(", ", selectedVersions)}]");
            
            // 测试设置选中状态
            BuildVersionConfigManager.SetVersionSelected("1.0.1", false);
            selectedVersions = BuildVersionConfigManager.GetSelectedVersions();
            Debug.Log($"取消选中1.0.1后，选中版本: [{string.Join(", ", selectedVersions)}]");
            
            Debug.Log("=== 版本号配置功能测试完成 ===");
        }
        
        [MenuItem("Build/Clear Version Config", priority = 101)]
        public static void ClearVersionConfig()
        {
            BuildVersionConfigManager.ClearVersions();
            Debug.Log("已清空版本号配置");
        }
    }
}
