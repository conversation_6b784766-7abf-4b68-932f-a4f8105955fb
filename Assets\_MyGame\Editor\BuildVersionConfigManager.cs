using UnityEngine;
using UnityEditor;
using System.IO;

namespace WGame.Editor
{
    /// <summary>
    /// 构建版本号配置管理器
    /// </summary>
    public static class BuildVersionConfigManager
    {
        private const string CONFIG_PATH = "Assets/_MyGame/Editor/BuildVersionConfig.asset";
        private static BuildVersionConfig _config;

        /// <summary>
        /// 获取配置实例
        /// </summary>
        public static BuildVersionConfig Config
        {
            get
            {
                if (_config == null)
                {
                    LoadConfig();
                }
                return _config;
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private static void LoadConfig()
        {
            _config = AssetDatabase.LoadAssetAtPath<BuildVersionConfig>(CONFIG_PATH);
            
            if (_config == null)
            {
                CreateDefaultConfig();
            }
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private static void CreateDefaultConfig()
        {
            // 确保目录存在
            string directory = Path.GetDirectoryName(CONFIG_PATH);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 创建配置文件
            _config = ScriptableObject.CreateInstance<BuildVersionConfig>();
            
            // 添加一些默认版本号
            _config.AddVersion("1.0.0");
            _config.AddVersion("1.0.1");
            
            // 保存配置文件
            AssetDatabase.CreateAsset(_config, CONFIG_PATH);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            Debug.Log($"创建默认构建版本配置: {CONFIG_PATH}");
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public static void SaveConfig()
        {
            if (_config != null)
            {
                EditorUtility.SetDirty(_config);
                AssetDatabase.SaveAssets();
            }
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public static void ReloadConfig()
        {
            _config = null;
            LoadConfig();
        }

        /// <summary>
        /// 获取选中的版本号列表
        /// </summary>
        public static string[] GetSelectedVersions()
        {
            return Config.GetSelectedVersions();
        }

        /// <summary>
        /// 添加版本号
        /// </summary>
        public static void AddVersion(string version)
        {
            Config.AddVersion(version);
            SaveConfig();
        }

        /// <summary>
        /// 移除版本号
        /// </summary>
        public static void RemoveVersion(string version)
        {
            Config.RemoveVersion(version);
            SaveConfig();
        }

        /// <summary>
        /// 移除指定索引的版本号
        /// </summary>
        public static void RemoveVersionAt(int index)
        {
            Config.RemoveVersionAt(index);
            SaveConfig();
        }

        /// <summary>
        /// 设置版本号选中状态
        /// </summary>
        public static void SetVersionSelected(int index, bool selected)
        {
            Config.SetVersionSelected(index, selected);
            SaveConfig();
        }

        /// <summary>
        /// 设置版本号选中状态
        /// </summary>
        public static void SetVersionSelected(string version, bool selected)
        {
            Config.SetVersionSelected(version, selected);
            SaveConfig();
        }

        /// <summary>
        /// 清空所有版本号
        /// </summary>
        public static void ClearVersions()
        {
            Config.ClearVersions();
            SaveConfig();
        }

        /// <summary>
        /// 全选/取消全选
        /// </summary>
        public static void SetAllSelected(bool selected)
        {
            Config.SetAllSelected(selected);
            SaveConfig();
        }

        /// <summary>
        /// 获取选中版本数量
        /// </summary>
        public static int GetSelectedCount()
        {
            return Config.GetSelectedCount();
        }

        /// <summary>
        /// 检查版本号是否已存在
        /// </summary>
        public static bool VersionExists(string version)
        {
            foreach (var item in Config.VersionItems)
            {
                if (item.version == version)
                    return true;
            }
            return false;
        }
    }
}
